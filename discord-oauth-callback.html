<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1.0"
		/>
		<link
			rel="stylesheet"
			href="style.css"
		/>
		<meta
			name="theme-color"
			content="#b38d70"
		/>
		<title><PERSON>er<PERSON> — Connected</title>
	</head>
	<body>
		<img
			class="ellipses"
			src="./assets/ellipses.webp"
			alt=""
		/>
		<header>
			<div class="header-spacer"></div>

			<div class="header-center">
				<!-- <PERSON>a Logo -->
				<svg
					width="68"
					height="68"
					viewBox="0 0 68 68"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<rect
						x="0.666626"
						y="0.666664"
						width="66.6667"
						height="66.6667"
						rx="18.3333"
						fill="#292524"
					/>
					<path
						fill-rule="evenodd"
						clip-rule="evenodd"
						d="M34.7406 22.3C38.5993 20.9587 47.8438 8.93234 52.7195 14.1426C57.5373 19.2911 55.3722 27.3298 55.3722 31.0183C55.3722 38.0601 54.4249 47.9664 49.0604 50.0021C44.0636 51.8983 40.0851 40.072 34.7406 40.072C27.8647 40.072 22.5927 57.7265 15.5823 54.8398C9.41957 52.3022 13.2994 42.3231 13.8814 35.6849C14.3888 29.8974 14.8759 24.0174 18.6703 19.6175C22.7448 14.8928 28.8472 24.3486 34.7406 22.3Z"
						fill="url(#paint0_linear_106_7)"
					/>
					<defs>
						<linearGradient
							id="paint0_linear_106_7"
							x1="33.9998"
							y1="12.8411"
							x2="33.9998"
							y2="55.1588"
							gradientUnits="userSpaceOnUse"
						>
							<stop stop-color="#BBA284" />
							<stop
								offset="1"
								stop-color="#FFF0DE"
							/>
						</linearGradient>
					</defs>
				</svg>

				<!-- Plus Icon -->
				<svg
					width="36"
					height="38"
					viewBox="0 0 36 38"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						d="M18.095 37.207C16.3723 37.207 15.2473 35.9062 15.2473 34.1836V21.8438H3.57544C1.85278 21.8438 0.552002 20.7188 0.552002 18.9961C0.552002 17.2734 1.85278 16.1484 3.57544 16.1484H15.2473V3.77344C15.2473 2.05078 16.3723 0.75 18.095 0.75C19.8176 0.75 20.9426 2.05078 20.9426 3.77344V16.1484H32.5793C34.302 16.1484 35.6028 17.2734 35.6028 18.9961C35.6028 20.7188 34.302 21.8438 32.5793 21.8438H20.9426V34.1836C20.9426 35.9062 19.8176 37.207 18.095 37.207Z"
						fill="black"
						fill-opacity="0.25"
					/>
				</svg>

				<!-- Discord Logo -->
				<svg
					width="67"
					height="66"
					viewBox="0 0 67 66"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<g clip-path="url(#clip0_107_74)">
						<path
							d="M56.2415 11.6265C51.9692 9.62757 47.4011 8.17485 42.6252 7.34766C42.0387 8.40809 41.3534 9.83439 40.881 10.969C35.8041 10.2055 30.7739 10.2055 25.7904 10.969C25.3181 9.83439 24.6173 8.40809 24.0255 7.34766C19.2445 8.17485 14.6711 9.6329 10.3988 11.6371C1.78152 24.6589 -0.554479 37.3572 0.613523 49.8753C6.32894 54.1434 11.8679 56.7362 17.3133 58.4328C18.6578 56.5824 19.857 54.6153 20.89 52.5423C18.9226 51.7947 17.0382 50.8721 15.2577 49.8011C15.7301 49.4512 16.1921 49.0853 16.6385 48.7088C27.4983 53.7882 39.2977 53.7882 50.0278 48.7088C50.4794 49.0853 50.9414 49.4512 51.4086 49.8011C49.6228 50.8774 47.7333 51.7999 45.7659 52.5476C46.7989 54.6153 47.9929 56.5877 49.3425 58.4381C54.7932 56.7415 60.3373 54.1488 66.0527 49.8753C67.4232 35.3637 63.7116 22.7819 56.2415 11.6265ZM22.3695 42.1768C19.1095 42.1768 16.436 39.1334 16.436 35.4273C16.436 31.7212 19.0524 28.6725 22.3695 28.6725C25.6867 28.6725 28.36 31.7158 28.3029 35.4273C28.3081 39.1334 25.6867 42.1768 22.3695 42.1768ZM44.2968 42.1768C41.0368 42.1768 38.3633 39.1334 38.3633 35.4273C38.3633 31.7212 40.9796 28.6725 44.2968 28.6725C47.6139 28.6725 50.2873 31.7158 50.2303 35.4273C50.2303 39.1334 47.6139 42.1768 44.2968 42.1768Z"
							fill="black"
						/>
					</g>
					<defs>
						<clipPath id="clip0_107_74">
							<rect
								width="66"
								height="66"
								fill="white"
								transform="translate(0.333252)"
							/>
						</clipPath>
					</defs>
				</svg>
			</div>
			<div class="header-links">
				<a
					href="#"
					class="header-link"
					onclick="openModal('tosModal')"
				>
					Terms of Service
					<svg
						width="12"
						height="12"
						viewBox="0 0 12 12"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<path
							d="M3.5 8.5L8.5 3.5M8.5 3.5H3.5M8.5 3.5V8.5"
							stroke="currentColor"
							stroke-width="1.5"
							stroke-linecap="round"
							stroke-linejoin="round"
						/>
					</svg>
				</a>
				<a
					href="#"
					class="header-link"
					onclick="openModal('privacyModal')"
				>
					Privacy Policy
					<svg
						width="12"
						height="12"
						viewBox="0 0 12 12"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<path
							d="M3.5 8.5L8.5 3.5M8.5 3.5H3.5M8.5 3.5V8.5"
							stroke="currentColor"
							stroke-width="1.5"
							stroke-linecap="round"
							stroke-linejoin="round"
						/>
					</svg>
				</a>
			</div>
		</header>

		<main>
			<section>
				<img
					class="trusted"
					src="./assets/trusted.webp"
					alt="Kaeru is a trusted app on Discord and has been verified by Discord. 250+ Individual Discord users trust Kaeru with their data."
				/>

				<div>
					<img
						class="kaeru_mascot"
						src="./assets/kaeru.webp"
						alt="Kaeru"
					/>

					<div role="text">
						<h1><span>Kaeru</span> + Your Account</h1>
						<p>You have linked your account with Kaeru.</p>
					</div>
				</div>

				<img
					class="kaeru_sticker"
					src="./assets/kaeru_sticker.webp"
					alt="Kaeru Sticker"
				/>
			</section>

			<button
				onclick="window.location.href = 'https://discord.com/oauth2/authorize?client_id=736561919292473454'"
			>
				<svg
					class="verified_icon"
					width="23"
					height="23"
					viewBox="0 0 23 23"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<g clip-path="url(#clip0_107_83)">
						<!-- Background seal (will rotate) -->
						<path
							class="verified_background"
							d="M13.1786 1.12908L14.6632 2.60564C14.8071 2.74334 14.9368 2.79041 15.1278 2.79041H17.2144C19.0196 2.79041 19.8608 3.64412 19.8608 5.43513V7.52967C19.8608 7.71443 19.9124 7.84861 20.0519 7.98631L21.5284 9.47088C22.7958 10.74 22.8038 11.9335 21.5284 13.2009L20.0519 14.6855C19.9159 14.8312 19.8608 14.9591 19.8608 15.1501V17.2367C19.8608 19.0419 19.0134 19.8831 17.2144 19.8831H15.1278C14.9368 19.8831 14.8089 19.9365 14.6632 20.0742L13.1786 21.5507C11.9112 22.8199 10.7159 22.8279 9.44857 21.5507L7.964 20.0742C7.82454 19.9365 7.69036 19.8831 7.50736 19.8831H5.41282C3.61556 19.8831 2.76634 19.0357 2.76634 17.2367V15.1501C2.76634 14.9591 2.71927 14.8312 2.58333 14.6855L1.10677 13.2009C-0.160611 11.9335 -0.168619 10.74 1.10677 9.47088L2.58333 7.98631C2.71927 7.85037 2.76634 7.71443 2.76634 7.52967V5.43513C2.76634 3.62635 3.6138 2.79041 5.41282 2.79041H7.50736C7.69036 2.79041 7.8263 2.74334 7.964 2.60564L9.44857 1.13084C10.7159 -0.140059 11.9077 -0.148068 13.1786 1.12908Z"
							fill="#43140740"
						/>
						<!-- Checkmark (will not rotate) -->
						<path
							class="verified_checkmark"
							d="M14.2343 7.49021L10.1962 13.9556L8.31575 11.5658C8.06282 11.2435 7.8304 11.1318 7.52884 11.1318C7.02513 11.1318 6.62884 11.5353 6.62884 12.0408C6.62884 12.2802 6.72025 12.5136 6.8931 12.737L9.28802 15.6482C9.55951 16.0023 9.86107 16.1538 10.23 16.1538C10.5937 16.1538 10.912 15.9747 11.1347 15.6411L15.7052 8.5156C15.8399 8.29919 15.9581 8.06052 15.9581 7.82712C15.9581 7.32341 15.5103 6.98123 15.0306 6.98123C14.723 6.98123 14.448 7.14881 14.2343 7.49021Z"
							fill="white"
						/>
					</g>
					<defs>
						<clipPath id="clip0_107_83">
							<rect
								width="22.6936"
								height="22.3425"
								fill="white"
								transform="translate(0.153229 0.174438)"
							/>
						</clipPath>
					</defs>
				</svg>

				<span>Install Kaeru</span> <sub>FREE</sub>
			</button>
		</main>

		<footer>
			<div class="big-text">Kaeru</div>
		</footer>

		<!-- Terms of Service Modal -->
		<div
			id="tosModal"
			class="modal"
		>
			<div class="modal-content">
				<div class="modal-header">
					<h1>📑 Kaeru – Terms of Service (ToS)</h1>
					<button
						class="modal-close"
						onclick="closeModal('tosModal')"
					>
						&times;
					</button>
				</div>
				<div class="modal-body">
					<p class="muted">Last Updated: August 24, 2025</p>
					<ol>
						<li>
							<strong>Acceptance of Terms</strong>
							<p>
								By using Kaeru ("the Bot"), you agree to these
								Terms of Service. If you do not agree, you may
								not use the Bot.
							</p>
						</li>
						<li>
							<strong>Eligibility</strong>
							<p>
								You must comply with Discord's Terms of Service
								and Community Guidelines. You must be at least
								13 years old (or the minimum age required in
								your country).
							</p>
						</li>
						<li>
							<strong>Use of the Bot</strong>
							<p>
								Kaeru provides moderation, ticketing, and
								AI-powered utility features. You agree not to
								misuse the Bot, attempt to exploit
								vulnerabilities, or use the Bot in violation of
								Discord's rules. The Bot may restrict, block, or
								remove your access if you violate these terms.
							</p>
						</li>
						<li>
							<strong>Disclaimer of Warranties</strong>
							<p>
								The Bot is provided "as is" without warranties
								of any kind. We are not responsible for
								downtime, data loss, or unexpected behavior.
							</p>
						</li>
						<li>
							<strong>Limitation of Liability</strong>
							<p>
								We are not liable for any damages, losses, or
								consequences resulting from your use of the Bot.
							</p>
						</li>
						<li>
							<strong>Termination</strong>
							<p>
								We may suspend or terminate access to the Bot at
								any time without notice if we believe you are
								violating the Terms or harming the service.
							</p>
						</li>
						<li>
							<strong>Changes to Terms</strong>
							<p>
								We may update these Terms from time to time.
								Continued use of the Bot means acceptance of the
								updated Terms.
							</p>
						</li>
					</ol>
					<p class="muted">
						If you have questions, please contact our support.
					</p>
				</div>
			</div>
		</div>

		<!-- Privacy Policy Modal -->
		<div
			id="privacyModal"
			class="modal"
		>
			<div class="modal-content">
				<div class="modal-header">
					<h1>📑 Kaeru – Privacy Policy</h1>
					<button
						class="modal-close"
						onclick="closeModal('privacyModal')"
					>
						&times;
					</button>
				</div>
				<div class="modal-body">
					<p class="muted">Last Updated: August 24, 2025</p>
					<ol>
						<li>
							<strong>Information We Collect</strong>
							<p>
								Kaeru may collect and process the following
								data: Discord User Data (User ID, Username,
								Avatar) Message Data (only when explicitly
								required for moderation, ticketing, or AI
								processing) Server Data (Guild ID, roles,
								channel IDs for configuration) We do not sell,
								trade, or share your data with third parties.
							</p>
						</li>
						<li>
							<strong>How We Use the Data</strong>
							<p>
								To provide core functionalities (moderation,
								tickets, AI utilities) To improve performance
								and security To comply with legal or Discord
								requirements
							</p>
						</li>
						<li>
							<strong>Data Storage & Retention</strong>
							<p>
								Data is stored securely and only as long as
								necessary for features. You may request deletion
								of your data via our support server or contact.
							</p>
						</li>
						<li>
							<strong>Third-Party Services</strong>
							<p>
								Kaeru may use third-party APIs (e.g., Google
								Generative AI). Use of these APIs is subject to
								their respective privacy policies.
							</p>
						</li>
						<li>
							<strong>User Rights</strong>
							<p>
								You can request access to or deletion of your
								stored data. You can opt-out by removing Kaeru
								from your server.
							</p>
						</li>
						<li>
							<strong>Security</strong>
							<p>
								We implement reasonable measures to protect your
								data, but we cannot guarantee 100% security.
							</p>
						</li>
						<li>
							<strong>Changes to this Policy</strong>
							<p>
								We may update this Privacy Policy from time to
								time. Continued use of the Bot means acceptance
								of the updated Policy.
							</p>
						</li>
					</ol>
					<p class="muted">
						If you have questions about privacy, please contact our
						support.
					</p>
				</div>
			</div>
		</div>

		<script>
			function openModal(modalId) {
				const modal = document.getElementById(modalId);
				modal.classList.add("show");
				document.body.style.overflow = "hidden";
			}

			function closeModal(modalId) {
				const modal = document.getElementById(modalId);
				modal.classList.remove("show");
				document.body.style.overflow = "auto";
			}

			window.onclick = function (event) {
				const modals = document.querySelectorAll(".modal");
				modals.forEach((modal) => {
					if (event.target === modal) {
						modal.classList.remove("show");
						document.body.style.overflow = "auto";
					}
				});
			};

			document.addEventListener("keydown", function (event) {
				if (event.key === "Escape") {
					const openModal = document.querySelector(".modal.show");
					if (openModal) {
						openModal.classList.remove("show");
						document.body.style.overflow = "auto";
					}
				}
			});
		</script>
	</body>
</html>
